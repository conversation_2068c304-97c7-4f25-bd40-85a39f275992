# FitGirl Easy Downloader

A Python-based tool that helps to easily download multiple files from fitgirl-repacks.site through fuckingfast.co. Original repository by [JOY6IX9INE](https://github.com/JOY6IX9INE/Fitgirl-Easy-Downloader).

## Features

- 🎮 Browse latest games from FitGirl Repacks
- 🔍 Enhanced search with advanced filtering and sorting
- 📂 Browse games by category/genre
- 🎯 Advanced filtering by size, date, and genre
- 📊 Multiple sorting options (relevance, title, size, date)
- 📄 Enhanced pagination with jump-to-page functionality
- ⬇️ Automatic download link extraction
- 📋 Download queue management
- 🎨 Colored terminal interface with metadata display
- 📁 Automatic file organization
- 🔄 Automatic link removal after successful download
<img width="863" alt="image" src="https://github.com/user-attachments/assets/c2b8f304-8f86-4072-93db-92793bf4e318" />
<img width="861" alt="image" src="https://github.com/user-attachments/assets/02b8a5da-5b9c-44ed-8fdd-1f853f268ce8" />
<img width="864" alt="image" src="https://github.com/user-attachments/assets/615dc9ee-7fd3-4f98-bd73-755de7147139" />
<img width="864" alt="image" src="https://github.com/user-attachments/assets/cc44a02a-22ef-40c6-a637-1d8c1f8a6acf" />


## Prerequisites

Ensure you have the following installed before running the script:

- Python 3.8+
- Required Python packages (install via `pip install -r requirements.txt`):
  - requests
  - beautifulsoup4
  - tqdm
  - colorama

## Installation

1. Clone the repository or download the source code
2. Install the required packages:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the script:
```bash
python main.py
```

2. The main menu offers the following options:
   - Browse Latest Games (with enhanced filtering and sorting)
   - Search Games (with advanced filters and sorting options)
   - Browse by Category (browse games by specific genres)
   - Process Download Queue
   - View Download Queue

3. To manually add download links:
   - Add your URLs to `input.txt`, one per line
   - Select "Process Download Queue" from the main menu

## Features in Detail

### Browse Latest Games
- View the latest game releases with metadata (size, genre, date)
- Enhanced pagination with jump-to-page functionality
- Advanced filtering by size, genre, and date range
- Multiple sorting options (date, title, size, relevance)
- View detailed game information
- Add download links to queue

### Search Games
- Enhanced search with advanced filtering capabilities
- Filter by game size (min/max in GB)
- Filter by genre/category
- Filter by release date range
- Sort results by relevance, title, size, or date
- Paginated results with improved navigation
- View search results with metadata
- Access detailed game information
- Add download links directly to queue

### Browse by Category
- Browse games by specific genres (Action, RPG, Strategy, etc.)
- Dedicated category browsing with pagination
- Quick access to games in your preferred genres

### Download Queue Management
- View all queued downloads
- Start downloading process
- Clear queue
- Automatic removal of completed downloads

### Download Process
- Automatic link extraction
- Progress bar for downloads
- Organized file storage in 'downloads' folder
- Colored terminal output for better visibility

## Disclaimer

This tool is created for educational purposes and ethical use only. Any misuse of this tool for malicious purposes is not condoned. The developers of this tool are not responsible for any illegal or unethical activities carried out using this tool.

## License

This project is licensed under the MIT License.

## Credits

Original repository by [JOY6IX9INE](https://github.com/JOY6IX9INE/Fitgirl-Easy-Downloader)

### Donations
If you feel like showing your love and/or appreciation for this Sipmle project, then how about shouting me a coffee :)

[<img src="https://github.com/zinzied/Website-login-checker/assets/10098794/24f9935f-3637-4607-8980-06124c2d0225">](https://www.buymeacoffee.com/Zied)
