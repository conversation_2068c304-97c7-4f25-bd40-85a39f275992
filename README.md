# FitGirl Easy Downloader

A Python-based tool that helps to easily download multiple files from fitgirl-repacks.site through fuckingfast.co. Original repository by [JOY6IX9INE](https://github.com/JOY6IX9INE/Fitgirl-Easy-Downloader).

## Features

- 🎮 Browse latest games from FitGirl Repacks
- 🔍 Search for specific games
- ⬇️ Automatic download link extraction
- 📋 Download queue management
- 🎨 Colored terminal interface
- 📁 Automatic file organization
- 🔄 Automatic link removal after successful download
<img width="863" alt="image" src="https://github.com/user-attachments/assets/c2b8f304-8f86-4072-93db-92793bf4e318" />
<img width="861" alt="image" src="https://github.com/user-attachments/assets/02b8a5da-5b9c-44ed-8fdd-1f853f268ce8" />
<img width="864" alt="image" src="https://github.com/user-attachments/assets/615dc9ee-7fd3-4f98-bd73-755de7147139" />
<img width="864" alt="image" src="https://github.com/user-attachments/assets/cc44a02a-22ef-40c6-a637-1d8c1f8a6acf" />


## Prerequisites

Ensure you have the following installed before running the script:

- Python 3.8+
- Required Python packages (install via `pip install -r requirements.txt`):
  - requests
  - beautifulsoup4
  - tqdm
  - colorama

## Installation

1. Clone the repository or download the source code
2. Install the required packages:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the script:
```bash
python main.py
```

2. The main menu offers the following options:
   - Browse Latest Games
   - Search Games
   - Process Download Queue
   - View Download Queue

3. To manually add download links:
   - Add your URLs to `input.txt`, one per line
   - Select "Process Download Queue" from the main menu

## Features in Detail

### Browse Latest Games
- View the latest game releases
- Navigate through multiple pages
- View detailed game information
- Add download links to queue

### Search Games
- Search for specific games
- View search results with descriptions
- Access detailed game information
- Add download links directly to queue

### Download Queue Management
- View all queued downloads
- Start downloading process
- Clear queue
- Automatic removal of completed downloads

### Download Process
- Automatic link extraction
- Progress bar for downloads
- Organized file storage in 'downloads' folder
- Colored terminal output for better visibility

## Disclaimer

This tool is created for educational purposes and ethical use only. Any misuse of this tool for malicious purposes is not condoned. The developers of this tool are not responsible for any illegal or unethical activities carried out using this tool.

## License

This project is licensed under the MIT License.

## Credits

Original repository by [JOY6IX9INE](https://github.com/JOY6IX9INE/Fitgirl-Easy-Downloader)

### Donations
If you feel like showing your love and/or appreciation for this Sipmle project, then how about shouting me a coffee :)

[<img src="https://github.com/zinzied/Website-login-checker/assets/10098794/24f9935f-3637-4607-8980-06124c2d0225">](https://www.buymeacoffee.com/Zied)
