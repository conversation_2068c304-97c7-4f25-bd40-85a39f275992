import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class FitGirlScraper:
    def __init__(self):
        self.base_url = "https://fitgirl-repacks.site"
        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'accept-language': 'en-US,en;q=0.5',
            'referer': 'https://fitgirl-repacks.site/',
            'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
        }
    
    def get_latest_games(self, page=1):
        """Get the latest games from the FitGirl website"""
        url = self.base_url
        if page > 1:
            url = f"{self.base_url}/page/{page}/"

        response = requests.get(url, headers=self.headers)
        if response.status_code != 200:
            return []

        soup = BeautifulSoup(response.text, 'html.parser')
        articles = soup.find_all('article')

        games = []
        for article in articles:
            title_element = article.find('h1', class_='entry-title') or article.find('h2', class_='entry-title')
            if not title_element:
                continue

            link_element = title_element.find('a')
            if not link_element:
                continue

            title = link_element.text.strip()
            link = link_element['href']

            # Get the thumbnail if available
            thumbnail = ""
            img_element = article.find('img')
            if img_element and 'src' in img_element.attrs:
                thumbnail = img_element['src']

            # Get the short description if available
            description = ""
            desc_element = article.find('div', class_='entry-content')
            if desc_element:
                description = desc_element.text.strip()[:200] + "..." if len(desc_element.text.strip()) > 200 else desc_element.text.strip()

            # Extract metadata from the article
            metadata = self._extract_game_metadata(article, title, desc_element)

            games.append({
                'title': title,
                'link': link,
                'thumbnail': thumbnail,
                'description': description,
                'size': metadata.get('size', 'Unknown'),
                'release_date': metadata.get('release_date', 'Unknown'),
                'genre': metadata.get('genre', 'Unknown'),
                'post_date': metadata.get('post_date', 'Unknown')
            })

        return games

    def _extract_game_metadata(self, article, title, content_element):
        """Extract metadata from game article"""
        metadata = {
            'size': 'Unknown',
            'release_date': 'Unknown',
            'genre': 'Unknown',
            'post_date': 'Unknown'
        }

        # Extract post date from article
        time_element = article.find('time')
        if time_element and time_element.get('datetime'):
            try:
                post_date = datetime.fromisoformat(time_element['datetime'].replace('Z', '+00:00'))
                metadata['post_date'] = post_date.strftime('%Y-%m-%d')
            except:
                pass

        # Extract size and other info from content
        if content_element:
            content_text = content_element.get_text().lower()

            # Extract file size
            size_patterns = [
                r'(\d+(?:\.\d+)?)\s*gb',
                r'(\d+(?:\.\d+)?)\s*mb',
                r'size[:\s]*(\d+(?:\.\d+)?)\s*gb',
                r'size[:\s]*(\d+(?:\.\d+)?)\s*mb'
            ]

            for pattern in size_patterns:
                match = re.search(pattern, content_text)
                if match:
                    size_value = float(match.group(1))
                    unit = 'GB' if 'gb' in pattern else 'MB'
                    if unit == 'MB' and size_value > 1024:
                        size_value = size_value / 1024
                        unit = 'GB'
                    metadata['size'] = f"{size_value:.1f} {unit}"
                    break

            # Extract genre from title or content
            genres = ['action', 'adventure', 'rpg', 'strategy', 'simulation', 'racing', 'sports',
                     'puzzle', 'horror', 'shooter', 'platformer', 'fighting', 'mmorpg', 'indie']

            title_lower = title.lower()
            for genre in genres:
                if genre in title_lower or genre in content_text:
                    metadata['genre'] = genre.title()
                    break

        return metadata

    def search_games(self, query, filters=None, sort_by='relevance'):
        """Search for games on the FitGirl website with optional filters and sorting"""
        search_url = f"{self.base_url}/?s={query}"
        response = requests.get(search_url, headers=self.headers)

        if response.status_code != 200:
            return []

        soup = BeautifulSoup(response.text, 'html.parser')
        articles = soup.find_all('article')

        games = []
        for article in articles:
            title_element = article.find('h1', class_='entry-title') or article.find('h2', class_='entry-title')
            if not title_element:
                continue

            link_element = title_element.find('a')
            if not link_element:
                continue

            title = link_element.text.strip()
            link = link_element['href']

            # Get the thumbnail if available
            thumbnail = ""
            img_element = article.find('img')
            if img_element and 'src' in img_element.attrs:
                thumbnail = img_element['src']

            # Get the short description if available
            description = ""
            desc_element = article.find('div', class_='entry-content')
            if desc_element:
                description = desc_element.text.strip()[:200] + "..." if len(desc_element.text.strip()) > 200 else desc_element.text.strip()

            # Extract metadata from the article
            metadata = self._extract_game_metadata(article, title, desc_element)

            game = {
                'title': title,
                'link': link,
                'thumbnail': thumbnail,
                'description': description,
                'size': metadata.get('size', 'Unknown'),
                'release_date': metadata.get('release_date', 'Unknown'),
                'genre': metadata.get('genre', 'Unknown'),
                'post_date': metadata.get('post_date', 'Unknown')
            }

            # Apply filters if provided
            if filters and not self._apply_filters(game, filters):
                continue

            games.append(game)

        # Sort the results
        games = self._sort_games(games, sort_by)

        return games

    def _apply_filters(self, game, filters):
        """Apply filters to a game"""
        # Size filter
        if filters.get('min_size') or filters.get('max_size'):
            size_str = game.get('size', 'Unknown')
            if size_str == 'Unknown':
                return False

            try:
                # Extract numeric value from size string
                size_value = float(size_str.split()[0])
                if 'MB' in size_str:
                    size_value = size_value / 1024  # Convert to GB

                if filters.get('min_size') and size_value < filters['min_size']:
                    return False
                if filters.get('max_size') and size_value > filters['max_size']:
                    return False
            except:
                return False

        # Genre filter
        if filters.get('genre'):
            game_genre = game.get('genre', 'Unknown').lower()
            if filters['genre'].lower() not in game_genre:
                return False

        # Date filter
        if filters.get('date_from') or filters.get('date_to'):
            post_date = game.get('post_date', 'Unknown')
            if post_date == 'Unknown':
                return False

            try:
                game_date = datetime.strptime(post_date, '%Y-%m-%d')
                if filters.get('date_from'):
                    filter_date = datetime.strptime(filters['date_from'], '%Y-%m-%d')
                    if game_date < filter_date:
                        return False
                if filters.get('date_to'):
                    filter_date = datetime.strptime(filters['date_to'], '%Y-%m-%d')
                    if game_date > filter_date:
                        return False
            except:
                return False

        return True

    def _sort_games(self, games, sort_by):
        """Sort games by specified criteria"""
        if sort_by == 'title':
            return sorted(games, key=lambda x: x['title'].lower())
        elif sort_by == 'size':
            def size_key(game):
                size_str = game.get('size', 'Unknown')
                if size_str == 'Unknown':
                    return 0
                try:
                    size_value = float(size_str.split()[0])
                    if 'MB' in size_str:
                        size_value = size_value / 1024
                    return size_value
                except:
                    return 0
            return sorted(games, key=size_key, reverse=True)
        elif sort_by == 'date':
            def date_key(game):
                post_date = game.get('post_date', 'Unknown')
                if post_date == 'Unknown':
                    return datetime.min
                try:
                    return datetime.strptime(post_date, '%Y-%m-%d')
                except:
                    return datetime.min
            return sorted(games, key=date_key, reverse=True)
        else:  # relevance (default order)
            return games

    def get_games_by_category(self, category, page=1):
        """Get games filtered by category/genre"""
        # Use search with category as query and filter by genre
        games = self.search_games(category, filters={'genre': category}, sort_by='date')

        # If no results with search, try browsing latest and filter
        if not games:
            all_games = self.get_latest_games(page)
            games = [game for game in all_games if category.lower() in game.get('genre', '').lower()]

        return games

    def get_available_genres(self):
        """Get list of available game genres"""
        return ['Action', 'Adventure', 'RPG', 'Strategy', 'Simulation', 'Racing', 'Sports',
                'Puzzle', 'Horror', 'Shooter', 'Platformer', 'Fighting', 'MMORPG', 'Indie']

    def get_game_details(self, game_url):
        """Get detailed information about a specific game"""
        response = requests.get(game_url, headers=self.headers)
        
        if response.status_code != 200:
            return None
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Get the title
        title_element = soup.find('h1', class_='entry-title')
        title = title_element.text.strip() if title_element else "Unknown Title"
        
        # Get the content
        content_element = soup.find('div', class_='entry-content')
        content = content_element.text.strip() if content_element else ""
        
        # Get the images
        images = []
        if content_element:
            img_elements = content_element.find_all('img')
            for img in img_elements:
                if 'src' in img.attrs:
                    images.append(img['src'])
        
        # Get the download links
        download_links = []
        if content_element:
            download_sections = content_element.find_all('div', class_='su-spoiler')
            for section in download_sections:
                section_title = section.find('div', class_='su-spoiler-title')
                if section_title and ('download' in section_title.text.lower() or 'mirror' in section_title.text.lower()):
                    links = section.find_all('a')
                    for link in links:
                        if 'href' in link.attrs and 'fuckingfast.co' in link['href']:
                            download_links.append(link['href'])
        
        # If no download links found in spoilers, try to find them directly
        if not download_links and content_element:
            links = content_element.find_all('a')
            for link in links:
                if 'href' in link.attrs and 'fuckingfast.co' in link['href']:
                    download_links.append(link['href'])
        
        # Extract system requirements
        system_requirements = ""
        if content_element:
            sys_req_section = None
            for h3 in content_element.find_all('h3'):
                if 'system requirements' in h3.text.lower():
                    sys_req_section = h3
                    break
            
            if sys_req_section:
                requirements = []
                next_element = sys_req_section.next_sibling
                while next_element and not (next_element.name == 'h3'):
                    if hasattr(next_element, 'text'):
                        requirements.append(next_element.text.strip())
                    next_element = next_element.next_sibling
                system_requirements = "\n".join([req for req in requirements if req])
        
        return {
            'title': title,
            'content': content,
            'images': images,
            'download_links': download_links,
            'system_requirements': system_requirements
        }
    
    def extract_download_links(self, game_url):
        """Extract all download links from a game page"""
        game_details = self.get_game_details(game_url)
        if not game_details:
            return []
        
        return game_details['download_links']
