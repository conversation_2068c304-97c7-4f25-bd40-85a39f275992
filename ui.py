import os
from colorama import Fore, Style, init
from datetime import datetime

# Initialize colorama
init()

class UI:
    def __init__(self):
        self.colors = {
            "green": Fore.GREEN, 
            "red": Fore.RED, 
            "yellow": Fore.YELLOW, 
            "blue": Fore.BLUE, 
            "magenta": Fore.MAGENTA, 
            "cyan": Fore.CYAN, 
            "white": Fore.WHITE, 
            "black": Fore.BLACK, 
            "reset": Style.RESET_ALL, 
            "lightblack": Fore.LIGHTBLACK_EX, 
            "lightred": Fore.LIGHTRED_EX, 
            "lightgreen": Fore.LIGHTGREEN_EX, 
            "lightyellow": Fore.LIGHTYELLOW_EX, 
            "lightblue": Fore.LIGHTBLUE_EX, 
            "lightmagenta": Fore.LIGHTMAGENTA_EX, 
            "lightcyan": Fore.LIGHTCYAN_EX, 
            "lightwhite": Fore.LIGHTWHITE_EX
        }

    def clear(self):
        os.system("cls" if os.name == "nt" else "clear")

    def timestamp(self):
        return datetime.now().strftime("%H:%M:%S")
    
    def success(self, message, obj=""):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightgreen']}SUCC {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors['lightgreen']}{obj}{self.colors['white']} {self.colors['reset']}")

    def error(self, message, obj=""):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightred']}ERRR {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors['lightred']}{obj}{self.colors['white']} {self.colors['reset']}")

    def done(self, message, obj=""):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightmagenta']}DONE {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors['lightmagenta']}{obj}{self.colors['white']} {self.colors['reset']}")

    def warning(self, message, obj=""):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightyellow']}WARN {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors['lightyellow']}{obj}{self.colors['white']} {self.colors['reset']}")

    def info(self, message, obj=""):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightblue']}INFO {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors['lightblue']}{obj}{self.colors['white']} {self.colors['reset']}")

    def custom(self, message, obj="", color="blue"):
        print(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors[color.lower()]}{color.upper()} {self.colors['lightblack']}• {self.colors['white']}{message} {self.colors[color.lower()]}{obj}{self.colors['white']} {self.colors['reset']}")

    def input(self, message):
        return input(f"{self.colors['lightblack']}{self.timestamp()} » {self.colors['lightcyan']}INPUT {self.colors['lightblack']}• {self.colors['white']}{message}{self.colors['reset']} ")

    def print_header(self, title):
        """Print a styled header"""
        width = os.get_terminal_size().columns
        print(f"{self.colors['lightblue']}{'=' * width}")
        print(f"{self.colors['lightcyan']}{title.center(width)}")
        print(f"{self.colors['lightblue']}{'=' * width}{self.colors['reset']}")

    def print_menu(self, options):
        """Print a menu with options"""
        for i, option in enumerate(options, 1):
            print(f"{self.colors['lightcyan']}{i}. {self.colors['white']}{option}")
        print(f"{self.colors['lightred']}0. {self.colors['white']}Exit{self.colors['reset']}")

    def print_game_list(self, games, start_index=0, show_metadata=True):
        """Print a list of games with index numbers and optional metadata"""
        if not games:
            self.warning("No games found", "")
            return

        for i, game in enumerate(games, start_index + 1):
            print(f"{self.colors['lightcyan']}{i}. {self.colors['lightgreen']}{game['title']}{self.colors['reset']}")

            # Show metadata if available and requested
            if show_metadata:
                metadata_parts = []
                if game.get('size') and game['size'] != 'Unknown':
                    metadata_parts.append(f"Size: {game['size']}")
                if game.get('genre') and game['genre'] != 'Unknown':
                    metadata_parts.append(f"Genre: {game['genre']}")
                if game.get('post_date') and game['post_date'] != 'Unknown':
                    metadata_parts.append(f"Date: {game['post_date']}")

                if metadata_parts:
                    metadata_str = " | ".join(metadata_parts)
                    print(f"   {self.colors['lightyellow']}{metadata_str}{self.colors['reset']}")

            if game.get('description'):
                print(f"   {self.colors['lightblack']}{game['description'][:100]}...{self.colors['reset']}")
            print()

    def print_game_details(self, game):
        """Print detailed information about a game"""
        self.clear()
        self.print_header(game['title'])
        
        # Print system requirements if available
        if game.get('system_requirements'):
            print(f"{self.colors['lightgreen']}System Requirements:{self.colors['reset']}")
            print(f"{self.colors['white']}{game['system_requirements']}{self.colors['reset']}")
            print()
        
        # Print download links if available
        if game.get('download_links'):
            print(f"{self.colors['lightgreen']}Download Links: {self.colors['lightcyan']}{len(game['download_links'])} links available{self.colors['reset']}")
            print()
        
        # Print content preview
        if game.get('content'):
            content_preview = game['content'][:500] + "..." if len(game['content']) > 500 else game['content']
            print(f"{self.colors['lightblack']}{content_preview}{self.colors['reset']}")
            print()

    def confirm(self, message):
        """Ask for confirmation (y/n)"""
        response = self.input(f"{message} (y/n): ").lower()
        return response == 'y' or response == 'yes'

    def print_filter_menu(self):
        """Print the filter options menu"""
        print(f"\n{self.colors['lightgreen']}Filter Options:{self.colors['reset']}")
        print(f"{self.colors['lightcyan']}1. {self.colors['white']}Filter by Size")
        print(f"{self.colors['lightcyan']}2. {self.colors['white']}Filter by Genre")
        print(f"{self.colors['lightcyan']}3. {self.colors['white']}Filter by Date Range")
        print(f"{self.colors['lightcyan']}4. {self.colors['white']}Clear All Filters")
        print(f"{self.colors['lightred']}0. {self.colors['white']}Back{self.colors['reset']}")

    def print_sort_menu(self):
        """Print the sorting options menu"""
        print(f"\n{self.colors['lightgreen']}Sort Options:{self.colors['reset']}")
        print(f"{self.colors['lightcyan']}1. {self.colors['white']}By Relevance (Default)")
        print(f"{self.colors['lightcyan']}2. {self.colors['white']}By Title (A-Z)")
        print(f"{self.colors['lightcyan']}3. {self.colors['white']}By Size (Largest First)")
        print(f"{self.colors['lightcyan']}4. {self.colors['white']}By Date (Newest First)")
        print(f"{self.colors['lightred']}0. {self.colors['white']}Back{self.colors['reset']}")

    def get_size_filter(self):
        """Get size filter from user"""
        print(f"\n{self.colors['lightgreen']}Size Filter (in GB):{self.colors['reset']}")
        min_size = self.input("Minimum size (leave empty for no limit): ")
        max_size = self.input("Maximum size (leave empty for no limit): ")

        filters = {}
        try:
            if min_size.strip():
                filters['min_size'] = float(min_size)
            if max_size.strip():
                filters['max_size'] = float(max_size)
        except ValueError:
            self.error("Invalid size format", "Please enter numbers only")
            return None

        return filters if filters else None

    def get_date_filter(self):
        """Get date filter from user"""
        print(f"\n{self.colors['lightgreen']}Date Filter (YYYY-MM-DD format):{self.colors['reset']}")
        date_from = self.input("From date (leave empty for no limit): ")
        date_to = self.input("To date (leave empty for no limit): ")

        filters = {}
        try:
            if date_from.strip():
                # Validate date format
                datetime.strptime(date_from, '%Y-%m-%d')
                filters['date_from'] = date_from
            if date_to.strip():
                # Validate date format
                datetime.strptime(date_to, '%Y-%m-%d')
                filters['date_to'] = date_to
        except ValueError:
            self.error("Invalid date format", "Please use YYYY-MM-DD format")
            return None

        return filters if filters else None

    def print_active_filters(self, filters):
        """Print currently active filters"""
        if not filters:
            return

        print(f"\n{self.colors['lightyellow']}Active Filters:{self.colors['reset']}")
        if filters.get('min_size'):
            print(f"  Min Size: {filters['min_size']} GB")
        if filters.get('max_size'):
            print(f"  Max Size: {filters['max_size']} GB")
        if filters.get('genre'):
            print(f"  Genre: {filters['genre']}")
        if filters.get('date_from'):
            print(f"  From Date: {filters['date_from']}")
        if filters.get('date_to'):
            print(f"  To Date: {filters['date_to']}")
        print()

    def print_pagination_info(self, current_page, total_results=None):
        """Print pagination information and controls"""
        print(f"\n{self.colors['lightblue']}Page {current_page}{self.colors['reset']}", end="")
        if total_results:
            print(f" ({total_results} results)")
        else:
            print()

        print(f"{self.colors['lightcyan']}n. {self.colors['white']}Next Page")
        print(f"{self.colors['lightcyan']}p. {self.colors['white']}Previous Page")
        print(f"{self.colors['lightcyan']}j. {self.colors['white']}Jump to Page")
        print(f"{self.colors['lightcyan']}f. {self.colors['white']}Filters & Sorting")
        print(f"{self.colors['lightred']}0. {self.colors['white']}Back to Main Menu{self.colors['reset']}")

    def get_page_number(self):
        """Get page number from user for jumping"""
        try:
            page = int(self.input("Enter page number: "))
            return page if page > 0 else None
        except ValueError:
            self.error("Invalid page number", "Please enter a valid number")
            return None
